/**
 * 문제 등록/수정 폼 JavaScript
 */

$(document).ready(function() {
    initializeForm();
    bindEvents();
    
    // 수정 모드인 경우 기존 데이터 로드
    if (window.isEdit && window.quizId) {
        loadExistingQuizData();
        // 수정 모드에서는 게임 유형 변경 불가
        $('#quizType').prop('disabled', true);
    }
});

/**
 * 폼 초기화
 */
function initializeForm() {
    // CSRF 토큰 설정
    $.ajaxSetup({
        beforeSend: function(xhr) {
            xhr.setRequestHeader(window.csrfHeader, window.csrfToken);
        }
    });
    
    // 언어 선택 초기화
    initializeLanguageSelection();
}

/**
 * 이벤트 바인딩
 */
function bindEvents() {
    // 최상위 카테고리 변경 시 하위 카테고리 로드
    $('#parentCategory').on('change', function() {
        const parentId = $(this).val();
        loadChildCategories(parentId);
    });
    
    // 언어 체크박스 변경 시 처리
    $('input[name="languages"]').on('change', function() {
        validateLanguageSelection();
    });
    
    // 폼 제출
    $('#quizForm').on('submit', function(e) {
        console.log('폼 제출 이벤트 발생');
        e.preventDefault();
        submitForm();
    });
    
    // 목록으로 버튼
    $('#backBtn').on('click', function() {
        window.location.href = '/manage/quiz/list';
    });
    
    // 삭제 버튼 (수정 모드에서만)
    $('#deleteBtn').on('click', function() {
        const quizId = $(this).data('quiz-id');
        deleteQuiz(quizId);
    });
    
    // QR 연동 버튼
    $('#qrMappingBtn').on('click', function() {
        alert('QR 연동 기능은 추후 구현 예정입니다.');
    });

    // 제출 버튼 클릭 이벤트
    $('#submitBtn').on('click', function(e) {
        console.log('제출 버튼 클릭됨');
        e.preventDefault();
        submitForm();
    });
}

/**
 * 하위 카테고리 로드
 */
function loadChildCategories(parentId) {
    const $childSelect = $('#categoryId');
    
    // 하위 카테고리 선택 박스 초기화
    $childSelect.empty().append('<option value="">하위 카테고리를 선택하세요</option>');
    
    if (!parentId) {
        return;
    }
    
    // AJAX로 하위 카테고리 조회
    $.ajax({
        url: `/manage/quiz/category/${parentId}/children`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                response.data.forEach(function(category) {
                    $childSelect.append(`<option value="${category.categoryId}">${category.categoryName}</option>`);
                });
            } else {
                console.error('하위 카테고리 로드 실패:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('하위 카테고리 로드 중 오류 발생:', error);
            alert('하위 카테고리를 불러오는 중 오류가 발생했습니다.');
        }
    });
}

/**
 * 언어 선택 초기화
 */
function initializeLanguageSelection() {
    // 한국어는 항상 선택되어 있고 비활성화
    $('input[name="languages"][value="ko"]').prop('checked', true).prop('disabled', true);
}

/**
 * 언어 선택 유효성 검사
 */
function validateLanguageSelection() {
    // 한국어는 항상 선택되어야 함
    const koCheckbox = $('input[name="languages"][value="ko"]');
    if (!koCheckbox.prop('checked')) {
        koCheckbox.prop('checked', true);
        alert('한국어는 기본 언어로 반드시 선택되어야 합니다.');
    }
}

/**
 * 기존 문제 데이터 로드 (수정 모드)
 */
function loadExistingQuizData() {
    // 카테고리 정보가 있는 경우 하위 카테고리 로드
    const parentCategoryId = $('#parentCategory').val();
    if (parentCategoryId) {
        // 하위 카테고리 로드
        $.ajax({
            url: `/manage/quiz/category/${parentCategoryId}/children`,
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const $childSelect = $('#categoryId');
                    response.data.forEach(function(category) {
                        $childSelect.append(`<option value="${category.categoryId}">${category.categoryName}</option>`);
                    });

                    // 기존 선택된 카테고리 설정
                    const selectedCategoryId = $childSelect.data('selected-value');
                    if (selectedCategoryId) {
                        $childSelect.val(selectedCategoryId);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('하위 카테고리 로드 중 오류 발생:', error);
            }
        });
    }

    // 기존 지원 언어 설정 로드
    loadExistingSupportedLanguages();
}

/**
 * 기존 지원 언어 설정 로드
 */
function loadExistingSupportedLanguages() {
    if (!window.isEdit || !window.quizId) {
        return;
    }

    // 서버에서 지원 언어 정보 조회
    $.ajax({
        url: `/manage/quiz/${window.quizId}/detail`,
        type: 'GET',
        success: function(response) {
            if (response.success && response.data && response.data.languages) {
                const languages = response.data.languages;

                // 지원하는 언어 체크박스 설정
                languages.forEach(function(content) {
                    const $checkbox = $(`input[name="languages"][value="${content.langCode}"]`);
                    if ($checkbox.length > 0) {
                        $checkbox.prop('checked', true);
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('지원 언어 정보 로드 중 오류 발생:', error);
        }
    });
}

/**
 * 폼 제출
 */
function submitForm() {
    console.log('submitForm 호출됨');

    if (!validateForm()) {
        console.log('유효성 검사 실패');
        return;
    }

    console.log('유효성 검사 통과, 폼 데이터 수집 시작');
    const formData = collectFormData();
    const url = window.isEdit ? `/manage/quiz/${window.quizId}` : '/manage/quiz/add';
    const method = 'POST';
    
    // 버튼 비활성화
    $('#submitBtn').prop('disabled', true).text('처리 중...');
    
    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert(response.message || (window.isEdit ? '문제가 성공적으로 수정되었습니다.' : '문제가 성공적으로 등록되었습니다.'));
                window.location.href = '/manage/quiz';
            } else {
                alert(response.message || '처리 중 오류가 발생했습니다.');
            }
        },
        error: function(xhr, status, error) {
            console.error('폼 제출 중 오류 발생:', error);
            let errorMessage = '처리 중 오류가 발생했습니다.';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            alert(errorMessage);
        },
        complete: function() {
            // 버튼 활성화
            $('#submitBtn').prop('disabled', false).text(window.isEdit ? '수정' : '등록');
        }
    });
}

/**
 * 폼 데이터 수집
 */
function collectFormData() {
    // 수정 모드에서 비활성화된 필드도 값을 가져오기 위해 disabled 속성 임시 제거
    const $quizType = $('#quizType');
    const wasDisabled = $quizType.prop('disabled');
    if (wasDisabled) {
        $quizType.prop('disabled', false);
    }

    const formData = {
        title: $('#title').val().trim(),
        categoryId: parseInt($('#categoryId').val()),
        quizType: $quizType.val(),
        status: $('#status').val(),
        difficultyLevel: 1, // 기본값
        correctAnswer: '', // 추후 구현
        imageUrl: '', // 추후 구현
        hint: '', // 추후 구현
        deleteYn: 'N'
    };

    // disabled 속성 복원
    if (wasDisabled) {
        $quizType.prop('disabled', true);
    }
    
    // 지원 언어 수집 (언어 선택만, 문제 내용은 별도 페이지에서 처리)
    const supportedLanguages = [];
    $('input[name="languages"]:checked').each(function() {
        supportedLanguages.push($(this).val());
    });

    formData.supportedLanguages = supportedLanguages;
    
    return formData;
}

/**
 * 폼 유효성 검사
 */
function validateForm() {
    console.log('validateForm 시작');

    // 필수 필드 검사
    const requiredFields = [
        { id: '#title', name: '문제 제목' },
        { id: '#categoryId', name: '하위 카테고리' },
        { id: '#quizType', name: '게임 유형' }
    ];

    // 모든 필드의 에러 상태 초기화
    requiredFields.forEach(function(field) {
        $(field.id).removeClass('quiz-form-error');
    });

    // 필수 필드 검사
    for (let i = 0; i < requiredFields.length; i++) {
        const field = requiredFields[i];
        const $field = $(field.id);
        const value = $field.val();

        console.log(`검사 중: ${field.name}, 필드 ID: ${field.id}, 값: "${value}", 필드 존재: ${$field.length > 0}`);

        if ($field.length === 0) {
            console.log(`필드를 찾을 수 없음: ${field.id}`);
            alert(`필드를 찾을 수 없습니다: ${field.name}`);
            return false;
        }

        if (!value || value.trim() === '') {
            console.log(`필수 필드 누락: ${field.name}`);
            $field.addClass('quiz-form-error');
            alert(`${field.name}은(는) 필수 입력 항목입니다.`);
            $field.focus();
            return false;
        }
    }

    // 지원 언어 선택 검사
    const selectedLanguages = $('input[name="languages"]:checked').length;
    if (selectedLanguages === 0) {
        alert('최소 하나 이상의 언어를 선택해야 합니다.');
        return false;
    }

    console.log('모든 필수 필드 검증 통과');
    return true;
}

/**
 * 문제 삭제
 */
function deleteQuiz(quizId) {
    if (!confirm('정말로 이 문제를 삭제하시겠습니까?\n삭제된 문제는 복구할 수 없습니다.')) {
        return;
    }
    
    $.ajax({
        url: `/manage/quiz/${quizId}`,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                alert(response.message || '문제가 성공적으로 삭제되었습니다.');
                window.location.href = '/manage/quiz';
            } else {
                alert(response.message || '삭제 중 오류가 발생했습니다.');
            }
        },
        error: function(xhr, status, error) {
            console.error('삭제 중 오류 발생:', error);
            let errorMessage = '삭제 중 오류가 발생했습니다.';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            alert(errorMessage);
        }
    });
}
